# Docker Mirror - 路径解析版本

基于 Cloudflare Workers 的 Docker 镜像代理服务，支持多种镜像仓库的智能路由。

## 🚀 新特性：路径解析路由

### 使用方式

通过URL路径直接指定目标镜像仓库，无需复杂的域名配置或查询参数：

```bash
# 新的路径解析方式（推荐）
https://your-worker.domain.com/ghcr.io/goauthentik/server:2025.6.4
https://your-worker.domain.com/quay.io/prometheus/node-exporter:latest
https://your-worker.domain.com/gcr.io/google-containers/pause:3.2

# 默认使用 Docker Hub（不指定仓库）
https://your-worker.domain.com/nginx:latest
https://your-worker.domain.com/mysql:8.0
```

### 支持的镜像仓库

- `ghcr.io` - GitHub Container Registry
- `quay.io` - Red Hat Quay
- `gcr.io` - Google Container Registry
- `k8s.gcr.io` - Kubernetes Container Registry
- `registry.k8s.io` - Kubernetes Registry
- `docker.cloudsmith.io` - Cloudsmith Docker Registry
- `nvcr.io` - NVIDIA Container Registry
- `registry-1.docker.io` - Docker Hub (默认)

### Docker 客户端使用示例

```bash
# 拉取 GitHub Container Registry 镜像
docker pull your-worker.domain.com/ghcr.io/goauthentik/server:2025.6.4

# 拉取 Quay 镜像
docker pull your-worker.domain.com/quay.io/prometheus/node-exporter:latest

# 拉取 Google Container Registry 镜像
docker pull your-worker.domain.com/gcr.io/google-containers/pause:3.2

# 拉取 Docker Hub 镜像（默认）
docker pull your-worker.domain.com/nginx:latest
```



## 🔧 部署配置

### 1. 部署到 Cloudflare Workers

```bash
# 克隆项目
git clone <repository-url>
cd Workers/docker-mirror

# 安装依赖
npm install

# 部署
npm run deploy
```

### 2. 自定义配置

修改 `src/index.ts` 中的配置：

```typescript
// 自定义工作服务器地址
let workersUrl = 'https://your-domain.com/';

// 添加新的镜像仓库
const KNOWN_REGISTRIES = [
    'quay.io',
    'gcr.io',
    // ... 其他仓库
    'your-custom-registry.com'  // 添加自定义仓库
];
```

## 🚀 核心特性

### 📍 路径解析路由
- ✅ **直观性**：URL直接显示目标镜像仓库
- ✅ **简洁性**：无需复杂的域名配置或查询参数
- ✅ **扩展性**：添加新仓库只需更新仓库列表
- ✅ **维护性**：代码逻辑清晰，易于维护
- ✅ **用户体验**：符合用户直觉的URL结构

### 🔐 高级Token预获取
- 🎯 **智能检测**：自动识别需要token的API路径
- 🚀 **性能优化**：预获取token，减少请求失败
- 🔄 **自动回退**：token获取失败时自动使用普通模式
- 📊 **兼容性提升**：显著提高Docker API成功率

### 🛠️ 工具函数增强
- 🛡️ **安全性**：安全的URL构造和错误处理
- 📝 **标准化**：统一的响应构造和请求头处理
- 🔍 **调试友好**：详细的日志输出和错误信息
- 🎯 **类型安全**：完整的TypeScript类型定义

## 🧪 测试

运行测试验证路径解析功能：

```bash
node test-path-parsing.js
```

## 📝 更新日志

### v2.1.0 - 增强版本
- ✨ 全新的路径解析路由功能
- 🔐 高级Token预获取逻辑，提升API兼容性
- 🛠️ 工具函数增强，提高代码健壮性
- 🔧 简化URL处理逻辑
- 📚 完善文档和示例
- 🧪 添加测试用例
- 🗑️ 移除复杂的域名前缀和查询参数方式

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License
