// Docker镜像仓库主机地址
const DEFAULT_HUB_HOST = 'registry-1.docker.io';
// Docker认证服务器地址
const AUTH_URL = 'https://auth.docker.io';
// 自定义的工作服务器地址
let workersUrl = 'https://docker.mirror.lilh.net/';

// 定义已知的镜像仓库域名列表
const KNOWN_REGISTRIES = [
	'quay.io',
	'gcr.io',
	'k8s.gcr.io',
	'registry.k8s.io',
	'ghcr.io',
	'docker.cloudsmith.io',
	'nvcr.io',
	'registry-1.docker.io'
];

/**
 * 从URL路径中解析镜像仓库和新的路径
 * @param pathname URL路径
 * @returns 返回解析结果
 */
function parseRegistryFromPath(pathname: string): {registry: string, newPath: string} {
	// 移除开头的斜杠并分割路径
	const pathParts = pathname.split('/').filter(Boolean);
	
	// 如果路径为空，返回默认配置
	if (pathParts.length === 0) {
		return {registry: DEFAULT_HUB_HOST, newPath: '/'};
	}
	
	// 特殊处理Docker API路径 /v2/...
	if (pathParts[0] === 'v2' && pathParts.length >= 2) {
		const secondPart = pathParts[1];
		
		// 检查第二部分是否为已知的镜像仓库域名
		if (KNOWN_REGISTRIES.includes(secondPart)) {
			return {
				registry: secondPart,
				newPath: '/v2/' + pathParts.slice(2).join('/')
			};
		}
		
		// 如果不是已知仓库，使用默认Docker Hub，保持原路径
		return {registry: DEFAULT_HUB_HOST, newPath: pathname};
	}
	
	const firstPart = pathParts[0];
	
	// 检查第一部分是否为已知的镜像仓库域名（非API路径）
	if (KNOWN_REGISTRIES.includes(firstPart)) {
		return {
			registry: firstPart,
			newPath: '/' + pathParts.slice(1).join('/')
		};
	}
	
	// 如果不是已知仓库，则认为是镜像名，使用默认Docker Hub
	return {registry: DEFAULT_HUB_HOST, newPath: pathname};
}

/** @type {RequestInit} */
const PREFLIGHT_INIT: RequestInit = {
	// 预检请求配置
	headers: new Headers({
		'access-control-allow-origin': '*',
		'access-control-allow-methods': 'GET,POST,PUT,PATCH,TRACE,DELETE,HEAD,OPTIONS',
		'access-control-max-age': '1728000',
	}),
}

export default {
	async fetch(request: Request): Promise<Response> {
		const url = new URL(request.url);
		workersUrl = `https://${url.hostname}`;

		// 使用路径解析方式确定镜像仓库
		const pathResult = parseRegistryFromPath(url.pathname);
		const hubHost = pathResult.registry;
		const newPath = pathResult.newPath;

		console.log(`原始路径: ${url.pathname}\n目标仓库: ${hubHost}\n新路径: ${newPath}`);

		// 更新URL的路径（如果使用了路径解析）
		if (newPath !== url.pathname) {
			url.pathname = newPath;
		}

		// 修改包含 %2F 和 %3A 的请求
		if (!/%2F/.test(url.search) && /%3A/.test(url.toString())) {
			url.search = url.search.replace(/%3A(?=.*?&)/, '%3Alibrary%2F');
			console.log(`handle_url: ${url}`);
		}

		// 处理token请求
		if (url.pathname.includes('/token')) {
			const tokenUrl = AUTH_URL + url.pathname + url.search;
			return fetch(new Request(tokenUrl, {
				...request,
				headers: new Headers({
					'Host': 'auth.docker.io',
					'User-Agent': request.headers.get("User-Agent") || '',
					'Accept': request.headers.get("Accept") || '',
					'Accept-Language': request.headers.get("Accept-Language") || '',
					'Accept-Encoding': request.headers.get("Accept-Encoding") || '',
					'Connection': 'keep-alive',
					'Cache-Control': 'max-age=0'
				})
			}));
		}

		// 修改 /v2/ 请求路径
		if (hubHost === DEFAULT_HUB_HOST && /^\/v2\/[^/]+\/[^/]+\/[^/]+$/.test(url.pathname) && !/^\/v2\/library/.test(url.pathname)) {
			url.pathname = '/v2/library/' + url.pathname.split('/v2/')[1];
			console.log(`modified_url: ${url.pathname}`);
		}

		// 更改请求的主机名
		url.hostname = hubHost;

		// 构造请求参数
		const headers = new Headers(request.headers);
		headers.set('Host', hubHost);

		// 发起请求并处理响应
		const originalResponse = await fetch(new Request(url, { ...request, headers }));
		const newHeaders = new Headers(originalResponse.headers);

		// 修改 Www-Authenticate 头
		const wwwAuthenticate = newHeaders.get("Www-Authenticate");
		if (wwwAuthenticate) {
			newHeaders.set("Www-Authenticate", wwwAuthenticate.replace(new RegExp(AUTH_URL, 'g'), workersUrl));
		}

		// 处理重定向
		const location = newHeaders.get("Location");
		if (location) {
			return httpHandler(request, location);
		}

		// 返回修改后的响应
		return new Response(originalResponse.body, {
			status: originalResponse.status,
			headers: newHeaders
		});
	}
};

/**
 * 处理HTTP请求
 * @param req 请求对象
 * @param pathname 请求路径
 * @returns 返回处理后的响应
 */
async function httpHandler(req: Request, pathname: string): Promise<Response> {
	if (req.method === 'OPTIONS' && req.headers.has('access-control-request-headers')) {
		return new Response(null, PREFLIGHT_INIT);
	}

	const url = new URL(pathname);
	return proxy(url, req);
}

/**
 * 代理请求
 * @param urlObj URL对象
 * @param req 请求对象
 * @returns 返回代理后的响应
 */
async function proxy(urlObj: URL, req: Request): Promise<Response> {
	const res = await fetch(urlObj.href, req);
	const headers = new Headers(res.headers);

	headers.set('access-control-expose-headers', '*');
	headers.set('access-control-allow-origin', '*');
	headers.set('Cache-Control', 'max-age=1500');

	// 删除不必要的头
	headers.delete('content-security-policy');
	headers.delete('content-security-policy-report-only');
	headers.delete('clear-site-data');

	return new Response(res.body, {
		status: res.status,
		headers: headers
	});
}
